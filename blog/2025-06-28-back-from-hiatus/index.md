---
slug: back-from-hiatus
title: I'm Back! PianoRhythm v0.10.0 Coming Soon
authors: [oak]
tags: [oak, pianorhythm, hiatus, v0.10.0, comeback, staging]
---

Hey everyone! Oak here, and I'm excited to announce that I'm back to actively working on PianoRhythm after several months of hiatus.

I know many of you have been wondering what's been going on, and I wanted to take a moment to update you all on where things stand and what's coming next.

<!-- truncate -->

## Where I've Been

Life has a way of throwing curveballs, and the past several months have been no exception for me. Between work commitments, personal matters, and the need to step back and recharge, I had to take some time away from active PianoRhythm development. 

I know this might have been frustrating for some of you who were eagerly waiting for updates, and I truly appreciate your patience and continued support during this time. The PianoRhythm community has always been incredible, and knowing that you've stuck around means the world to me.

## What's Coming: v0.10.0

I'm thrilled to share that I'm back in full development mode, and I have some exciting news: **PianoRhythm v0.10.0 is planned for release around August 2025!**

This upcoming version represents a significant milestone in PianoRhythm's journey. While I can't reveal all the details just yet, I can tell you that v0.10.0 will include:

- (Potential) Performance improvements and bug fixes
- Continued refinements to the audio engine
- UI/UX improvements
- Dedicated page for the sheet music repository
- And more!

I'll be sharing more specific details about the features and improvements as we get closer to the release date.

## Check Out the Staging Site

For those of you who want to get a sneak peek at what's coming, you can check out the latest development version on our **staging site** at [https://staging.pianorhythm.io](https://staging.pianorhythm.io). This is where I test new features and improvements before they make it to the main application.

Keep in mind that the staging site is for testing purposes, so you might encounter some bugs or incomplete features. But it's a great way to see the direction PianoRhythm is heading and provide feedback on new developments.

## Moving Forward

I'm committed to being more consistent with development and communication moving forward. The break, while necessary, has given me renewed energy and perspective on PianoRhythm's future.

I'll be posting more regular updates here on the blog and staying more active in the community. Your feedback, suggestions, and bug reports are invaluable in making PianoRhythm the best it can be.

## Thank You

Before I wrap up, I want to give a huge thank you to everyone who has continued to support PianoRhythm during my absence. Whether you've been playing regularly, sharing the app with friends, or just patiently waiting for updates - you're the reason I'm motivated to keep building and improving this platform.

PianoRhythm has always been a passion project, and seeing how much it means to the community makes all the hard work worthwhile.

Stay tuned for more updates as we approach the v0.10.0 release. I'm excited to share this journey with all of you!

As always, you can reach me at:
- Email: <EMAIL>
- Discord: Feel free to ping me in the PianoRhythm Discord server

Let's make some beautiful music together! 🎹

— Oak
