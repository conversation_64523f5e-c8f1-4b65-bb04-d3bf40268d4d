name: 'deploy-docs'
on:
  push:
    branches: ["main"]
    paths:
      - "docs/**"
      - "blog/**"
      - "src/**"
      - "static/**"
      - "changelog/**"
      - "docusaurus.config.js"
      - "sidebars.js"
      - "package.json"
      - "pnpm-lock.yaml"

  workflow_dispatch:

concurrency:
  group: docs_deployment
  cancel-in-progress: true

jobs:
  build-docs:
    runs-on: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v4

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 19.2.0

      - name: 📥 PNPM install
        uses: ./.github/actions/pnpm-install

      - name: Build Docs
        run: npm run build

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Publish Image to Container Registry
        uses: ./.github/actions/doctl-docker-publish
        with:
          image_path: pianorhythm/docs
          dockerfile: docs.Dockerfile