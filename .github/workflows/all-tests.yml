name: 'web-all-tests'
on:
  workflow_dispatch:
  pull_request:

concurrency:
  group: run_all_tests_job
  cancel-in-progress: true

env:
  WASM_BINDGEN_VERSION: 0.2.100
  RUST_TOOLCHAIN: nightly-2025-06-25

jobs:
  test:
    name: Run tests
    runs-on: [action-runner-1]
    permissions:
      checks: write
      contents: read
      issues: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v4

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 19.2.0

      - name: 📥 PNPM install
        uses: ./.github/actions/pnpm-install

      - name: 📥 Ensure Rust Artifacts
        id: rust-artifacts
        timeout-minutes: 120
        uses: ./.github/actions/ensure-rust-artifacts
        with:
          github-token: ${{ secrets.DOWNLOAD_ARTIFACTS_PAT }}
          build-renderer-webgpu: true
          rust-toolchain: ${{ env.RUST_TOOLCHAIN }}
          wasm-bindgen-version: ${{ env.WASM_BINDGEN_VERSION }}

      - name: 📊 Report Rust Build Status
        run: |
          echo "Core built from source: ${{ steps.rust-artifacts.outputs.core-built }}"
          echo "WebGPU built from source: ${{ steps.rust-artifacts.outputs.webgpu-built }}"
          echo "WebGL2 built from source: ${{ steps.rust-artifacts.outputs.webgl2-built }}"

      - name: 📥 Build protobuf
        run: chmod +x ./build/protoc/protoc_linux64 && node ./build-protobuf-ts.cjs

      - name: Run client tests
        run: pnpm test
        timeout-minutes: 120

      - name: Install Cypress
        if: false
        run: pnpm cypress install

      - name: Cypress run
        if: false
        uses: cypress-io/github-action@v6
        timeout-minutes: 120
        with:
          wait-on: http://localhost:4000
          wait-on-timeout: 120
          start: pnpm run cy:vite
