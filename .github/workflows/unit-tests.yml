name: 'web-unit-tests'
on:
  workflow_dispatch:
  push:

concurrency:
  group: run_unit_tests_job
  cancel-in-progress: true

env:
  WASM_BINDGEN_VERSION: 0.2.100
  RUST_TOOLCHAIN: nightly-2025-06-25

jobs:
  test:
    name: Run tests
    runs-on: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v4

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 19.2.0

      - name: 📥 PNPM install
        uses: ./.github/actions/pnpm-install

      - name: 📥 Ensure Rust Artifacts
        id: rust-artifacts
        timeout-minutes: 120
        uses: ./.github/actions/ensure-rust-artifacts
        with:
          github-token: ${{ secrets.DOWNLOAD_ARTIFACTS_PAT }}
          rust-toolchain: ${{ env.RUST_TOOLCHAIN }}
          wasm-bindgen-version: ${{ env.WASM_BINDGEN_VERSION }}

      - name: 📊 Report Rust Build Status
        run: |
          echo "Core built from source: ${{ steps.rust-artifacts.outputs.core-built }}"

      - name: 📥 Build protobuf
        run: chmod +x ./build/protoc/protoc_linux64 && node ./build-protobuf-ts.cjs

      - name: Run client tests
        run: pnpm test
        timeout-minutes: 120
