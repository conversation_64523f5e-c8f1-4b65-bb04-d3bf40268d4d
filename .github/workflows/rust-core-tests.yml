name: 'rust-tests'
on:
  workflow_dispatch:
    inputs:
      manual: {}

  pull_request:
    branches:
      - '*'

concurrency:
  group: environment-${{ github.ref }}
  cancel-in-progress: true

env:
  RUST_TOOLCHAIN: nightly-2025-06-25

jobs:
  run-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: 📥 Setup Rust
        uses: ./.github/actions/rust-setup
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          install-deps: true
          cache-key: "core-tests"

      - name: Run Synth Tests
        working-directory: ./pianorhythm_core
        run: cargo test --package pianorhythm_synth

      - name: Run Core Tests
        working-directory: ./pianorhythm_core
        run: cargo test --package pianorhythm_core --no-default-features
