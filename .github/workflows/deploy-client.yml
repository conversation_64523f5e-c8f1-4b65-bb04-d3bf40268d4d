name: 'deploy-web'
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging

concurrency:
  group: client_deployment
  cancel-in-progress: true

env:
  WASM_BINDGEN_VERSION: 0.2.100
  RUST_TOOLCHAIN: nightly-2025-06-25

jobs:
  build_locales:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout Locales
        uses: actions/checkout@v4
        with:
          repository: PianoRhythm/pianorhythm-locales
          token: ${{ secrets.PAT }}
          path: locales/

      - name: setup node
        uses: actions/setup-node@v3
        with:
          node-version: 19.2.0

      - name: Install and validate
        working-directory: locales
        run: |
          npm install
          npm run validate

  build_rust:
    runs-on: [action-runner-2]
    outputs:
      rust-changed: ${{ steps.changes.outputs.rust }}
    steps:
      - name: Check environment
        run: |
          if [[ ${{ inputs.environment }} == 'production' ]]; then
            echo "Environment is production, skipping Rust build"
            exit 1
          fi

      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            rust:
              - 'pianorhythm_core/**/*.rs'
              - 'pianorhythm_core/**/Cargo.toml'
              - 'pianorhythm_core/**/Cargo.lock'
              - 'pianorhythm_core/**/*.proto'
              - 'pianorhythm_core/**/*.sh'

      - name: 📥 Setup Rust
        uses: ./.github/actions/rust-setup
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          targets: wasm32-unknown-unknown
          cache-key: "deploy-client"

      - name: 📥 Setup Wasm-bindgen
        uses: ./.github/actions/wasm-bindgen-setup
        with:
          version: ${{ env.WASM_BINDGEN_VERSION }}

      - name: Cache Core Build
        uses: actions/cache@v4
        id: cache-core-build
        with:
          path: |
            pianorhythm_core/pkg/snippets/
            pianorhythm_core/pkg/pianorhythm_core*
          key: ${{ runner.os }}-${{ inputs.environment }}-core-build-${{ env.RUST_TOOLCHAIN }}-${{ env.WASM_BINDGEN_VERSION }}-${{ hashFiles('**/pianorhythm_core/Cargo.lock','**/pianorhythm_core/core/**/*.rs', '**/pianorhythm_core/synth/**/*.rs', '**/pianorhythm_core/shared/**/*.rs', '**/pianorhythm_core/proto/**/*.rs', '**/pianorhythm_core/proto/**/*.proto') }}
          restore-keys: |
            ${{ runner.os }}-${{ inputs.environment }}-core-build-${{ env.RUST_TOOLCHAIN }}-${{ env.WASM_BINDGEN_VERSION }}-
            ${{ runner.os }}-${{ inputs.environment }}-core-build-${{ env.RUST_TOOLCHAIN }}-
            ${{ runner.os }}-${{ inputs.environment }}-core-build-
      

      - name: Run rust tests workflow
        if: steps.changes.outputs.rust == 'true'
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: rust-tests
          token: ${{ secrets.DOWNLOAD_ARTIFACTS_PAT }}

      - name: 📥 Build core
        if: steps.cache-core-build.outputs.cache-hit != 'true'
        working-directory: pianorhythm_core
        timeout-minutes: 120
        run: chmod +x ./build-core-release.sh && ./build-core-release.sh

      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: pianorhythm_core
          path: |
            pianorhythm_core/pkg/
            pianorhythm_core/proto/
          if-no-files-found: error
          overwrite: true

  build_renderer_webgpu:
    runs-on: [action-runner-1]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Renderer Build
        uses: actions/cache@v4
        id: cache-renderer-webgpu-build
        with:
          path: |
            pianorhythm_core/pkg/snippets/
            pianorhythm_core/pkg/webgpu/
          key: ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgpu-${{ env.RUST_TOOLCHAIN }}-${{ env.WASM_BINDGEN_VERSION }}-${{ hashFiles('pianorhythm_core/Cargo.lock', 'pianorhythm_core/bevy_renderer/**/*.rs') }}
          restore-keys: |
            ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgpu-${{ env.RUST_TOOLCHAIN }}-${{ env.WASM_BINDGEN_VERSION }}-
            ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgpu-${{ env.RUST_TOOLCHAIN }}-
            ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgpu-

      - name: 📥 Setup Rust
        if: steps.cache-renderer-webgpu-build.outputs.cache-hit != 'true'
        uses: ./.github/actions/rust-setup
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          targets: wasm32-unknown-unknown
          cache-key: "deploy-client-webgpu"

      - name: 📥 Setup Wasm-bindgen
        uses: ./.github/actions/wasm-bindgen-setup
        with:
          version: ${{ env.WASM_BINDGEN_VERSION }}

      - name: Build Renderer (WebGPU)
        if: steps.cache-renderer-webgpu-build.outputs.cache-hit != 'true'
        working-directory: ./pianorhythm_core
        timeout-minutes: 120
        run: chmod +x ./build-bevy-renderer-wasm-webgpu.sh && ./build-bevy-renderer-wasm-webgpu.sh

      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: pianorhythm_core_webgpu
          path: |
            pianorhythm_core/pkg/
            pianorhythm_core/proto/
          if-no-files-found: error
          overwrite: true

  build_renderer_webgl2:
    runs-on: [action-runner-2]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Renderer Build
        uses: actions/cache@v4
        id: cache-renderer-webgl-build
        with:
          path: |
            pianorhythm_core/pkg/snippets/
            pianorhythm_core/pkg/webgl2/
          key: ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgl2-${{ env.RUST_TOOLCHAIN }}-${{ env.WASM_BINDGEN_VERSION }}-${{ hashFiles('pianorhythm_core/Cargo.lock', 'pianorhythm_core/bevy_renderer/**/*.rs') }}
          restore-keys: |
            ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgl2-${{ env.RUST_TOOLCHAIN }}-${{ env.WASM_BINDGEN_VERSION }}-
            ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgl2-${{ env.RUST_TOOLCHAIN }}-
            ${{ runner.os }}-${{ inputs.environment }}-renderer-build-webgl2-

      - name: 📥 Setup Rust
        if: steps.cache-renderer-webgl-build.outputs.cache-hit != 'true'
        uses: ./.github/actions/rust-setup
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          targets: wasm32-unknown-unknown
          cache-key: "deploy-client-webgl2"

      - name: 📥 Setup Wasm-bindgen
        uses: ./.github/actions/wasm-bindgen-setup
        with:
          version: ${{ env.WASM_BINDGEN_VERSION }}

      - name: Build Renderer (WebGL2)
        if: steps.cache-renderer-webgl-build.outputs.cache-hit != 'true'
        working-directory: ./pianorhythm_core
        timeout-minutes: 120
        run: chmod +x ./build-bevy-renderer-wasm-webgl2.sh && ./build-bevy-renderer-wasm-webgl2.sh

      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: pianorhythm_core_webgl2
          path: |
            pianorhythm_core/pkg/
            pianorhythm_core/proto/
          if-no-files-found: error
          overwrite: true

  build_client:
    needs: [build_locales, build_rust, build_renderer_webgpu, build_renderer_webgl2]
    runs-on: [action-runner-1]
    outputs:
      PR_CLIENT_BUILD_DATE: ${{ steps.build_client.outputs.PR_CLIENT_BUILD_DATE }}
    steps:
      - uses: actions/checkout@v4

      - name: Run client tests workflow
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: web-all-tests
          token: ${{ secrets.DOWNLOAD_ARTIFACTS_PAT }}

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 19.2.0

      - name: 📥 PNPM install
        uses: ./.github/actions/pnpm-install

      - name: 📥 Download Rust Artifacts
        uses: ./.github/actions/download-rust-artifacts

      - name: 📥 Build protobuf
        run: chmod +x ./build/protoc/protoc_linux64 && node ./build-protobuf-ts.cjs

      - name: 🔄 Update .env
        run: |
          echo "PR_CLIENT_VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_ENV
          sed -i "s/VITE_VERSION=.*/VITE_VERSION=$PR_CLIENT_VERSION/g" .env

      - name: Get commit hash date and convert to datetime ms
        run: |
          echo "PR_DEPLOY_COMMIT_DATE=$(git show -s --format=%cd)" >> $GITHUB_ENV

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            tests:
              - 'tests/**'
              - 'src/**'

      - name: Cache Client Build
        uses: actions/cache@v4
        id: cache-client-build
        with:
          path: |
            .output
          key: ${{ runner.os }}-${{ inputs.environment }}-client-build-${{ env.PR_CLIENT_VERSION }}-${{ hashFiles('**/pnpm-lock.yaml', 'get-build-date.js', '**/.env*', 'build-protobuf-ts.cjs', 'tsconfig.json', 'app.config.ts', '**/src/**/*.ts', '**/src/**/*.js', '**/src/**/*.tsx', '**/src/**/*.jsx') }}
          restore-keys: |
            ${{ runner.os }}-${{ inputs.environment }}-client-build-${{ env.PR_CLIENT_VERSION }}-
            ${{ runner.os }}-${{ inputs.environment }}-client-build-

      - name: Get Build Date
        run: |
          echo "PR_CLIENT_BUILD_DATE=$(git show -s --format=%cd)" >> $GITHUB_ENV

      - name: Feed build date variable to node to parse as date
        run: |
          echo "PR_CLIENT_BUILD_DATE=$(node -p "new Date('${{ env.PR_CLIENT_BUILD_DATE }}').getTime()")" >> $GITHUB_OUTPUT

      - name: 📥 Build client
        if: steps.cache-client-build.outputs.cache-hit != 'true'
        timeout-minutes: 120
        run: pnpm build:${{ inputs.environment }}
        env:
          NODE_ENV: ${{ inputs.environment }}
          BUILD_ENV: ${{ inputs.environment }}
          PR_DEPLOY_COMMIT_DATE: ${{ env.PR_DEPLOY_COMMIT_DATE }}

      - name: Display structure of output files
        run: ls .output

      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: client
          path: .output
          if-no-files-found: error
          overwrite: true
          include-hidden-files: true

  deploy:
    needs: build_client
    runs-on: [ubuntu-latest]
    env:
      PR_CLIENT_BUILD_DATE: ${{ needs.build_client.outputs.PR_CLIENT_BUILD_DATE }}
    steps:
      - uses: actions/checkout@v4

      - name: Download Artifacts from client
        uses: actions/download-artifact@v4
        with:
          name: client
          path: .output/

      - name: 📥 Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Login to registry
        shell: bash
        run: doctl registry login --expiry-seconds 600

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:v0.12.0

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 19.2.0

      - name: Get Client Version
        run: |
          echo "PR_CLIENT_VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_ENV

      - name: Create TAG environment variable
        run: |
          if [[ ${{ inputs.environment }} == 'staging' ]]; then
            echo "TAG=client-express-server-stg" >> $GITHUB_ENV
          else
            echo "TAG=client-express-server" >> $GITHUB_ENV
          fi

      - name: Create PIANORHYTHM_SERVER_URL environment variable
        run: |
          if [[ ${{ inputs.environment }} == 'staging' ]]; then
            echo "PIANORHYTHM_SERVER_URL=https://staging.pianorhythm.io" >> $GITHUB_ENV
          else
            echo "PIANORHYTHM_SERVER_URL=https://pianorhythm.io" >> $GITHUB_ENV
          fi

      - name: Create PIANORHYTHM_MONGODB_API_DATASOURCE environment variable
        run: |
          if [[ ${{ inputs.environment }} == 'staging' ]]; then
            echo "PIANORHYTHM_MONGODB_API_DATASOURCE=v3-staging-cluster" >> $GITHUB_ENV
          else
            echo "PIANORHYTHM_MONGODB_API_DATASOURCE=v3-prod-cluster" >> $GITHUB_ENV
          fi

      - name: 🚀 Build Server and push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: registry.digitalocean.com/pianorhythm/${{ env.TAG }}:latest
          file: DockerFile
          cache-from: type=gha
          cache-to: type=gha,mode=max
          secrets: |
            PIANORHYTHM_ANALYTICS_TOKEN=${{ secrets.PIANORHYTHM_ANALYTICS_TOKEN }}
            PIANORHYTHM_MONGODB_API_KEY_EXPRESS=${{ secrets.PIANORHYTHM_MONGODB_API_KEY_EXPRESS }}
          build-args: |
            NODE_ENV=${{ inputs.environment }}
            PR_CLIENT_VERSION=${{ env.PR_CLIENT_VERSION }}
            PR_ASSETS_URL=${{ vars.PIANORHYTHM_ASSETS_CDN_URL }}
            PR_CDN_URL=${{ vars.PIANORHYTHM_CDN_URL }}
            PIANORHYTHM_SERVER_URL=${{ env.PIANORHYTHM_SERVER_URL }}
            PIANORHYTHM_MONGODB_API_HOST=${{ vars.PIANORHYTHM_MONGODB_API_HOST }}
            PIANORHYTHM_MONGODB_API_DATASOURCE=${{ env.PIANORHYTHM_MONGODB_API_DATASOURCE }}

      - name: Update client version in MongoDB
        run: |
          echo "Updating client version in server settings..."
          curl -X POST "${{ vars.PIANORHYTHM_MONGO_DATA_API_UPDATE_WEB_VERSION }}?serviceName=${{ env.PIANORHYTHM_MONGODB_API_DATASOURCE }}&versionFieldName=latestWebVersion&version=${{ env.PR_CLIENT_VERSION }}" \
            -H "Content-Type: application/json" \
            -H 'Access-Control-Request-Headers: *' \
            -H "apiKey: ${{ secrets.PIANORHYTHM_MONGODB_DATA_API_KEY_GITHUB }}" \
            -d '{}'

      - name: Update client build date in MongoDB
        env:
          PR_CLIENT_BUILD_DATE: ${{ env.PR_CLIENT_BUILD_DATE }}
        run: |
          echo "Updating client build date in server settings..."
          curl -X POST "${{ vars.PIANORHYTHM_MONGO_DATA_API_UPDATE_WEB_VERSION }}?serviceName=${{ env.PIANORHYTHM_MONGODB_API_DATASOURCE }}&versionFieldName=latestWebBuildDate&version=${{ env.PR_CLIENT_BUILD_DATE }}" \
            -H "Content-Type: application/json" \
            -H 'Access-Control-Request-Headers: *' \
            -H "apiKey: ${{ secrets.PIANORHYTHM_MONGODB_DATA_API_KEY_GITHUB }}" \
            -d '{}'

      - uses: sarisia/actions-status-discord@v1
        if: success()
        with:
          webhook: ${{ secrets.DISCORD_WEBHOOK }}
          nodetail: true
          title: A new update of PianoRhythm (web) has been deployed!
          description: |
            `Version`: ${{ env.PR_CLIENT_VERSION }}
            `Build Date`: ${{ env.PR_CLIENT_BUILD_DATE }}
            `Environment`: ${{ inputs.environment }}
            `Platform`: Web

            Check it out here: [PianoRhythm](${{ env.PIANORHYTHM_SERVER_URL }})!
