########################################################################################
# "Ensure Rust artifacts" composite action for test workflows                       #
#--------------------------------------------------------------------------------------#
# This action will:                                                                   #
# 1. Try to download existing artifacts                                               #
# 2. If artifacts are missing, build them from source                                 #
# 3. Ensure all required artifacts are available for testing                          #
#                                                                                      #
# Usage in workflows steps:                                                           #
#                                                                                      #
#      - name: 📥 Ensure Rust Artifacts                                               #
#        uses: ./.github/actions/ensure-rust-artifacts                                #
#        with:                                                                        #
#          github-token: ${{ secrets.DOWNLOAD_ARTIFACTS_PAT }} # (optional)          #
#          build-renderers: true # (default: false)                                   #
#                                                                                      #
########################################################################################

name: 'Ensure Rust Artifacts'
description: 'Download or build Rust artifacts as needed for testing'

inputs:
  github-token:
    description: 'GitHub token for downloading artifacts from other runs'
    required: false
    default: ''
  build-renderer-webgpu:
    description: 'Whether to build renderer artifacts (WebGPU) if missing'
    required: false
    default: 'false'
  build-renderer-webgl2:
    description: 'Whether to build renderer artifacts (WebGL2) if missing'
    required: false
    default: 'false'
  path:
    description: 'Path to download/build artifacts to'
    required: false
    default: 'pianorhythm_core/'
  rust-toolchain:
    description: 'Rust toolchain version to use'
    required: false
    default: 'nightly-2025-06-25'
  wasm-bindgen-version:
    description: 'Wasm-bindgen version to use'
    required: false
    default: '0.2.100'

outputs:
  core-built:
    description: 'Whether the core was built from source'
    value: ${{ steps.check-core.outputs.built }}
  webgpu-built:
    description: 'Whether WebGPU renderer was built from source'
    value: ${{ steps.check-webgpu.outputs.built }}
  webgl2-built:
    description: 'Whether WebGL2 renderer was built from source'
    value: ${{ steps.check-webgl2.outputs.built }}

runs:
  using: 'composite'
  steps:
    # First, try to download existing artifacts
    - name: 📥 Try to download existing artifacts
      uses: ./.github/actions/download-rust-artifacts
      with:
        path: ${{ inputs.path }}
        github-token: ${{ inputs.github-token }}
      continue-on-error: true  # Allow workflow to continue even if download fails

    # Check if core artifacts exist
    - name: Check core artifacts
      id: check-core
      shell: bash
      run: |
        if [ -f "${{ inputs.path }}pkg/pianorhythm_core.js" ] && [ -f "${{ inputs.path }}pkg/pianorhythm_core_bg.wasm" ]; then
          echo "Core artifacts found"
          echo "built=false" >> $GITHUB_OUTPUT
        else
          echo "Core artifacts missing, will build from source"
          echo "built=true" >> $GITHUB_OUTPUT
        fi

    # Build core if missing
    - name: 📥 Setup Rust for core build
      if: steps.check-core.outputs.built == 'true'
      uses: ./.github/actions/rust-setup
      with:
        toolchain: ${{ inputs.rust-toolchain }}
        targets: wasm32-unknown-unknown
        cache-key: "test-core-build"

    - name: 📥 Setup Wasm-bindgen for core build
      if: steps.check-core.outputs.built == 'true'
      uses: ./.github/actions/wasm-bindgen-setup
      with:
        version: ${{ inputs.wasm-bindgen-version }}

    - name: 📥 Build core from source
      if: steps.check-core.outputs.built == 'true'
      shell: bash
      working-directory: ${{ inputs.path }}
      run: |
        echo "Building Rust core from source..."
        chmod +x ./build-core-release.sh && ./build-core-release.sh

    # Check and build WebGPU renderer if requested
    - name: Check WebGPU renderer artifacts
      if: inputs.build-renderer-webgpu == 'true'
      id: check-webgpu
      shell: bash
      run: |
        if [ -d "${{ inputs.path }}pkg/webgpu" ] && [ -f "${{ inputs.path }}pkg/webgpu/pianorhythm_core.js" ]; then
          echo "WebGPU renderer artifacts found"
          echo "built=false" >> $GITHUB_OUTPUT
        else
          echo "WebGPU renderer artifacts missing, will build from source"
          echo "built=true" >> $GITHUB_OUTPUT
        fi

    - name: 📥 Build WebGPU renderer from source
      if: inputs.build-renderer-webgpu == 'true' && steps.check-webgpu.outputs.built == 'true'
      shell: bash
      working-directory: ${{ inputs.path }}
      run: |
        echo "Building WebGPU renderer from source..."
        chmod +x ./build-bevy-renderer-wasm-webgpu.sh && ./build-bevy-renderer-wasm-webgpu.sh

    # Check and build WebGL2 renderer if requested
    - name: Check WebGL2 renderer artifacts
      if: inputs.build-renderer-webgl2 == 'true'
      id: check-webgl2
      shell: bash
      run: |
        if [ -d "${{ inputs.path }}pkg/webgl2" ] && [ -f "${{ inputs.path }}pkg/webgl2/pianorhythm_core.js" ]; then
          echo "WebGL2 renderer artifacts found"
          echo "built=false" >> $GITHUB_OUTPUT
        else
          echo "WebGL2 renderer artifacts missing, will build from source"
          echo "built=true" >> $GITHUB_OUTPUT
        fi

    - name: 📥 Build WebGL2 renderer from source
      if: inputs.build-renderer-webgl2 == 'true' && steps.check-webgl2.outputs.built == 'true'
      shell: bash
      working-directory: ${{ inputs.path }}
      run: |
        echo "Building WebGL2 renderer from source..."
        chmod +x ./build-bevy-renderer-wasm-webgl2.sh && ./build-bevy-renderer-wasm-webgl2.sh

    # Verify all required artifacts are now available
    - name: Verify artifacts
      shell: bash
      run: |
        echo "Verifying core artifacts..."
        if [ ! -f "${{ inputs.path }}pkg/pianorhythm_core.js" ] || [ ! -f "${{ inputs.path }}pkg/pianorhythm_core_bg.wasm" ]; then
          echo "❌ Core artifacts are still missing after build attempt"
          exit 1
        fi
        echo "✅ Core artifacts verified"
        
        if [ "${{ inputs.build-renderer-webgpu }}" == "true" ]; then
          echo "Verifying WebGPU renderer artifacts..."
          if [ ! -d "${{ inputs.path }}pkg/webgpu" ] || [ ! -f "${{ inputs.path }}pkg/webgpu/pianorhythm_bevy_renderer.js" ]; then
            echo "❌ WebGPU renderer artifacts are missing"
            exit 1
          fi
          echo "✅ WebGPU renderer artifacts verified"
        fi
        
        if [ "${{ inputs.build-renderer-webgl2 }}" == "true" ]; then
          echo "Verifying WebGL2 renderer artifacts..."
          if [ ! -d "${{ inputs.path }}pkg/webgl2" ] || [ ! -f "${{ inputs.path }}pkg/webgl2/pianorhythm_bevy_renderer.js" ]; then
            echo "❌ WebGL2 renderer artifacts are missing"
            exit 1
          fi
          echo "✅ WebGL2 renderer artifacts verified"
        fi
        
        echo "🎉 All required artifacts are available"
