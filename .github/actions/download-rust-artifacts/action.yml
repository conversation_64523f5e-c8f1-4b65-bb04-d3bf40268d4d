########################################################################################
# "Download Rust artifacts" composite action for consistent artifact downloading     #
#--------------------------------------------------------------------------------------#
# Usage in workflows steps:                                                           #
#                                                                                      #
#      - name: 📥 Download Rust Artifacts                                             #
#        uses: ./.github/actions/download-rust-artifacts                              #
#        with:                                                                        #
#          path: pianorhythm_core/ # (default)                                        #
#          github-token: ${{ secrets.DOWNLOAD_ARTIFACTS_PAT }} # (optional)          #
#                                                                                      #
########################################################################################

name: 'Download Rust Artifacts'
description: 'Download all Rust core artifacts (core, WebGPU, WebGL2)'

inputs:
  path:
    description: 'Path to download artifacts to'
    required: false
    default: 'pianorhythm_core/'
  github-token:
    description: 'GitHub token for downloading artifacts from other runs'
    required: false
    default: ''

runs:
  using: 'composite'
  steps:
    - name: Download Artifacts from Rust core
      uses: actions/download-artifact@v4
      with:
        name: pianorhythm_core
        path: ${{ inputs.path }}
        github-token: ${{ inputs.github-token }}
      continue-on-error: true

    - name: Download Artifacts from Rust core (WebGPU)
      uses: actions/download-artifact@v4
      with:
        name: pianorhythm_core_webgpu
        path: ${{ inputs.path }}
        github-token: ${{ inputs.github-token }}
      continue-on-error: true

    - name: Download Artifacts from Rust core (WebGL2)
      uses: actions/download-artifact@v4
      with:
        name: pianorhythm_core_webgl2
        path: ${{ inputs.path }}
        github-token: ${{ inputs.github-token }}
      continue-on-error: true
