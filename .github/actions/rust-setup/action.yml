########################################################################################
# "Rust setup" composite action for consistent Rust toolchain setup                 #
#--------------------------------------------------------------------------------------#
# Requirement: Should be run on ubuntu-latest                                         #
#                                                                                      #
# Usage in workflows steps:                                                           #
#                                                                                      #
#      - name: 📥 Setup Rust                                                          #
#        uses: ./.github/actions/rust-setup                                           #
#        with:                                                                        #
#          toolchain: ${{ env.RUST_TOOLCHAIN }} # (default from env)                 #
#          targets: wasm32-unknown-unknown # (optional)                               #
#          components: rust-src # (optional)                                          #
#          install-deps: true # (default: true)                                       #
#                                                                                      #
########################################################################################

name: 'Rust Setup'
description: 'Setup Rust toolchain with common dependencies and caching'

inputs:
  toolchain:
    description: 'Rust toolchain to install'
    required: false
    default: 'nightly-2025-06-25'
  targets:
    description: 'Additional targets to install (space-separated)'
    required: false
    default: ''
  components:
    description: 'Additional components to install (space-separated)'
    required: false
    default: 'rust-src'
  install-deps:
    description: 'Whether to install system dependencies'
    required: false
    default: 'true'
  cache-key:
    description: 'Additional cache key suffix'
    required: false
    default: 'default'

runs:
  using: 'composite'
  steps:
    - name: 📥 Rust install
      uses: dtolnay/rust-toolchain@master
      with:
        toolchain: ${{ inputs.toolchain }}
        targets: ${{ inputs.targets }}
        components: ${{ inputs.components }}

    - name: 📥 Install system dependencies
      if: inputs.install-deps == 'true'
      shell: bash
      run: |
        sudo apt-get update
        sudo apt-get -y install gcc libasound2-dev libudev-dev librust-alsa-sys-dev

    - name: Setup Rust cache
      uses: Swatinem/rust-cache@v2
      with:
        key: ${{ inputs.cache-key }}
        workspaces: "pianorhythm_core"
        cache-on-failure: "true"
