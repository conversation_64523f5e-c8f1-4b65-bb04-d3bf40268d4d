########################################################################################
# "pnpm install" composite action for pnpm 9                                        #
#--------------------------------------------------------------------------------------#
# Requirement: @setup/node should be run before                                        #
#                                                                                      #
# Usage in workflows steps:                                                            #
#                                                                                      #
#      - name: 📥 Monorepo install                                                     #
#        uses: ./.github/actions/pnpm-install                                          #
#        with:                                                                         #
#          cwd: ${{ github.workspace }}/apps/my-app # (default = '.')                  #
#                                                                                      #
# Reference:                                                                           #
#   - latest: https://gist.github.com/belgattitude/838b2eba30c324f1f0033a797bab2e31    #
#                                                                                      #
# Versions:                                                                            #
#   - 1.1.0 - 15-07-2023 - Add project custom directory support.
#   - 1.1.1 - 29-08-2024 - Updated action to use pnpm/action-setup@v4.                 #
########################################################################################

name: 'PNPM install'
description: 'Run pnpm install with cache enabled'

inputs:
  cwd:
    description: "Changes node's process.cwd() if the project is not located on the root. Default to process.cwd()"
    required: false
    default: '.'

runs:
  using: 'composite'

  steps:
    - uses: pnpm/action-setup@v4
      name: Install pnpm

    - name: Expose pnpm config(s) through "$GITHUB_OUTPUT"
      id: pnpm-config
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

    - name: Cache rotation keys
      id: cache-rotation
      shell: bash
      run: |
        echo "YEAR_MONTH=$(/bin/date -u "+%Y%m")" >> $GITHUB_OUTPUT

    - uses: actions/cache@v4
      name: Setup pnpm cache
      with:
        path: ${{ steps.pnpm-config.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-cache-${{ steps.cache-rotation.outputs.YEAR_MONTH }}-${{ hashFiles('pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-cache-${{ steps.cache-rotation.outputs.YEAR_MONTH }}-

    - name: Install dependencies
      shell: bash
      working-directory: ${{ inputs.cwd }}
      run: pnpm install --frozen-lockfile --prefer-offline
      env:
        # Other environment variables
        HUSKY: '0' # By default do not run HUSKY install
