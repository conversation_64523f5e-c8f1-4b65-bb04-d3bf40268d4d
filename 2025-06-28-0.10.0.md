---
  date: 2025-06-28
  version: 0.10.0
  tags:
    - 0.10.0
    - changelog
  
---

# 0.10.0

> **Note:** This version is not yet released. Expected release date: Mid-August 2025.

## :rocket: New Features
  - **Bevy Engine Update to 0.16** Major renderer upgrade with improved performance and stability. The 3D renderer now uses the latest Bevy engine for better graphics and more efficient rendering.
  
  - **Enhanced MIDI Handling** Improved WebMIDI integration with better audio worklet support and enhanced event handling for MIDI input devices.
  
  - **Admin Dashboard Components** Added new administrative features and enhanced login response types for better user management.
  
  - **Server Commands Re-added** Restored server commands including client commands (/nickname, etc), server mod commands, and room owner commands with comprehensive test coverage.
  
  - **Customizable Graphics Settings** Added customizable graphics settings dropdown for better user control over rendering performance and quality.

## :smile: Enhancements
  - **Sheet Music Repository Improvements** Enhanced error handling in SheetMusicService with improved user feedback and better pagination button behavior.
  
  - **CI/CD Infrastructure Optimization** Migrated to self-hosted Linux runners and optimized GitHub workflows with composite actions for Rust setup, wasm-bindgen installation, and artifact downloading.
  
  - **Testing Infrastructure** Added comprehensive unit tests for user service, AppService, and AppLoading components with improved mock services for authentication flow.
  
  - **Dependency Updates** Updated wasm-bindgen and related packages, upgraded bevy_egui and bevy-async-ecs for better compatibility and performance.
  
  - **Code Refactoring** Major refactor for improved code clarity and performance, including enhanced type safety in function signatures and cleaner event handling.
  
  - **Audio Processing Improvements** Enhanced audio worklet integration with better polyfill support for improved browser compatibility.

## :bug: Bug Fixes
  - **Fixed Reverb Settings** Resolved issue where reverb settings were not being applied after the OxiSynth refactor.
  
  - **Fixed Keyboard Input in WebGL2** Resolved issue where keyboard keys were not working in the WebGL2 renderer.
  
  - **Fixed Chat Message Z-Index** Fixed chat messages appearing behind the piano in 2D mode by correcting the rendering order.
  
  - **Fixed Login Issues** Resolved staging environment login problems that were causing serialization errors.
  
  - **Fixed Discord OAuth Redirect** Fixed Discord OAuth not redirecting to the passed redirect URL parameter correctly.
  
  - **Fixed Graphics Smearing** Resolved issue with graphic smearing in the renderer for cleaner visual output.
  
  - **Fixed Sheet Music Details Layout** Centered the body content in sheet music details view for better presentation.

<!----------------------------------------------->
